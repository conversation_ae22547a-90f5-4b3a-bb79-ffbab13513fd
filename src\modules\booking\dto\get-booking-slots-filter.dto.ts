import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsOptional,
  IsString,
  IsEnum,
  Min,
  Max,
  IsInt,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class GetBookingSlotsFilterDto {
  bookingPageId: string;

  @ApiProperty({
    description: 'Specific date to filter (YYYY-MM-DD)',
    example: '2025-05-22',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  date?: string;

  @ApiProperty({
    description: 'Start date for date range filter (YYYY-MM-DD)',
    example: '2025-05-20',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  dateFrom?: string;

  @ApiProperty({
    description: 'End date for date range filter (YYYY-MM-DD)',
    example: '2025-05-25',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  dateTo?: string;

  @ApiProperty({
    description: 'Field ID to filter',
    example: 'field-1',
    required: false,
  })
  @IsOptional()
  @IsString()
  field?: string;

  @ApiProperty({
    description: 'Time slot to filter',
    example: '08:00',
    required: false,
  })
  @IsOptional()
  @IsString()
  time?: string;

  @ApiProperty({
    description: 'Booking status to filter',
    enum: ['pending', 'confirmed', 'cancelled', 'completed'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['pending', 'confirmed', 'cancelled', 'completed'])
  status?: 'pending' | 'confirmed' | 'cancelled' | 'completed';

  @ApiProperty({
    description: 'Number of results to return',
    example: 50,
    required: false,
    minimum: 1,
    maximum: 1000,
  })
  @IsOptional()
  @Transform(({ value }) => {
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? undefined : parsed;
  })
  @IsInt()
  @Min(1)
  @Max(1000)
  limit?: number;

  @ApiProperty({
    description: 'Number of results to skip',
    example: 0,
    required: false,
    minimum: 0,
  })
  @IsOptional()
  @Transform(({ value }) => {
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? undefined : parsed;
  })
  @IsInt()
  @Min(0)
  offset?: number;
}
