import {
  Controller,
  Get,
  Param,
  Put,
  Body,
  UseGuards,
  Query,
  ValidationPipe,
  NotFoundException,
} from '@nestjs/common';
import { BookingService } from '../../booking/booking.service';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiBearerAuth,
  Api<PERSON>uery,
  ApiProperty,
} from '@nestjs/swagger';
import { AuthGuard } from 'src/common/guards/auth.guard';
import { GetUser } from 'src/common/decorators/get-user.decorator';

class UpdateBookingStatusDto {
  @ApiProperty({ enum: ['pending', 'confirmed', 'cancelled', 'completed'] })
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
}

@ApiTags('agent/booking')
@Controller('agent/booking')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class BookingAgentController {
  constructor(private readonly bookingService: BookingService) {}

  @Get()
  @ApiOperation({ summary: 'Get all bookings for a booking page' })
  @ApiResponse({ status: 200, description: 'Return all bookings' })
  @ApiQuery({
    name: 'bookingPageId',
    required: true,
    description: 'Booking page ID',
  })
  async findAll(
    @Query('bookingPageId') bookingPageId: string,
    @GetUser('_id') userId: string,
  ) {
    if (!bookingPageId) {
      throw new NotFoundException('Booking page ID is required');
    }

    // The service will check if the user has access to the booking page
    const data = await this.bookingService.findAll({
      bookingPageId,
    });
    return { data };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a booking by ID' })
  @ApiResponse({ status: 200, description: 'Return the booking' })
  @ApiResponse({ status: 404, description: 'Booking not found' })
  async findOne(@Param('id') id: string, @GetUser('_id') userId: string) {
    try {
      const data = await this.bookingService.findOne(id);
      return { data };
    } catch (error: any) {
      throw new NotFoundException(error?.message || 'Booking not found');
    }
  }

  @Put(':id/status')
  @ApiOperation({ summary: 'Update booking status' })
  @ApiResponse({
    status: 200,
    description: 'Booking status updated successfully',
  })
  @ApiResponse({ status: 404, description: 'Booking not found' })
  async updateStatus(
    @Param('id') id: string,
    @Body(new ValidationPipe()) updateStatusDto: UpdateBookingStatusDto,
    @GetUser('_id') userId: string,
  ) {
    try {
      const data = await this.bookingService.updateStatus(
        id,
        updateStatusDto.status,
        userId,
      );
      return { data };
    } catch (error: any) {
      throw new NotFoundException(
        error?.message || 'Failed to update booking status',
      );
    }
  }
}
