import { Injectable } from '@nestjs/common';
import { BookingRepository } from './repositories/booking.repository';
import { CreateBookingDto } from './dto/create-booking.dto';
import { Booking } from './schemas/booking.schema';
import { <PERSON><PERSON>r<PERSON><PERSON>per } from 'src/common/services/error.helper';
import { ErrorMessages } from './constants/error.constants';
import { BookingPageService } from '../booking-page/booking-page.service';
import { NotificationService } from '../notification/services/notification.service';
import { BookingPage } from '../booking-page/schemas/booking-page.schema';
import {
  BookingSlotsFilterResponse,
  BookingFilterOptions,
} from './interfaces/booked-slot.interface';
import { GetBookingSlotsFilterDto } from './dto/get-booking-slots-filter.dto';

/**
 * Interface for validation results
 */
interface ValidationResult {
  isValid: boolean;
  errorKey?: keyof typeof ErrorMessages;
  errorMessage?: string;
}

/**
 * Interface for booking creation context
 * Contains all data needed during the booking creation process
 */
interface BookingCreationContext {
  dto: CreateBookingDto;
  bookingPage: BookingPage;
  validationResults: ValidationResult[];
}

@Injectable()
export class BookingService {
  constructor(
    private readonly bookingRepository: BookingRepository,
    private readonly bookingPageService: BookingPageService,
    private readonly notificationService: NotificationService,
  ) {}

  /**
   * Create a new booking with comprehensive validation and error handling
   * @param createBookingDto The booking data
   * @returns The created booking
   */
  async create(createBookingDto: CreateBookingDto): Promise<Booking> {
    console.log(
      'Creating new booking:',
      JSON.stringify(createBookingDto, null, 2),
    );

    try {
      // Create a context object to pass data between validation steps
      const context: BookingCreationContext = {
        dto: createBookingDto,
        bookingPage: null,
        validationResults: [],
      };

      console.log('Step 1: Validating booking page');
      // 1. Validate booking page
      await this.validateBookingPage(context);

      console.log('Step 2: Validating booking data');
      // 2. Validate booking data
      await this.validateBookingData(context);

      console.log('Step 3: Checking for validation errors');
      // 3. Check for validation errors
      this.handleValidationErrors(context);

      console.log('Step 4: Creating booking record');
      // 4. Create the booking
      const booking = await this.createBookingRecord(context);
      console.log(`Booking created with ID: ${booking._id}`);

      console.log('Step 5: Sending notifications');
      // 5. Send notifications
      await this.sendBookingNotifications(context, booking);

      return booking;
    } catch (error) {
      console.error('Error creating booking:', error);
      this.handleBookingError(error);
    }
  }

  /**
   * Validate the booking page exists and is active
   * @param context The booking creation context
   */
  private async validateBookingPage(
    context: BookingCreationContext,
  ): Promise<void> {
    console.log(`Validating booking page with slug: ${context.dto.slug}`);

    try {
      // Get booking page by slug
      const bookingPage = await this.bookingPageService.findBySlug(
        context.dto.slug,
      );

      // Check if booking page exists
      if (!bookingPage) {
        console.log('Booking page not found');
        context.validationResults.push({
          isValid: false,
          errorKey: 'booking/booking-page-not-found',
        });
        return;
      }

      console.log(
        `Found booking page: ${bookingPage._id}, status: ${bookingPage.status}`,
      );

      // Check if booking page is active
      if (bookingPage.status !== 'active') {
        console.log('Booking page is not active');
        context.validationResults.push({
          isValid: false,
          errorKey: 'booking/booking-page-not-active',
        });
        return;
      }

      // Store booking page in context for later use
      context.bookingPage = bookingPage;

      // Validate that the slug matches the bookingPageId
      console.log(
        `Comparing bookingPageId: ${context.dto.bookingPageId} with page._id: ${bookingPage._id.toString()}`,
      );
      if (bookingPage._id.toString() !== context.dto.bookingPageId) {
        console.log('Slug does not match bookingPageId');
        context.validationResults.push({
          isValid: false,
          errorKey: 'booking/slug-id-mismatch',
        });
      } else {
        console.log('Slug matches bookingPageId');
      }
    } catch (error) {
      console.error('Error validating booking page:', error);
      context.validationResults.push({
        isValid: false,
        errorKey: 'booking/booking-page-not-found',
      });
    }
  }

  /**
   * Validate booking data including duplicate checks and slot availability
   * @param context The booking creation context
   */
  private async validateBookingData(
    context: BookingCreationContext,
  ): Promise<void> {
    console.log('Validating booking data');

    // Skip validation if booking page validation failed
    if (!context.bookingPage) {
      console.log(
        'Skipping booking data validation because booking page validation failed',
      );
      return;
    }

    // Check for duplicate bookings
    // console.log('Checking for duplicate bookings');
    // const isDuplicate = await this.checkDuplicateBooking(context);

    // if (isDuplicate) {
    //   console.log('Duplicate booking found');
    //   context.validationResults.push({
    //     isValid: false,
    //     errorKey: 'booking/duplicate-booking',
    //   });
    //   return; // Skip slot validation if duplicate booking found
    // }

    // Check slot availability
    console.log('Checking slot availability');
    await this.validateBookingSlots(context);
  }

  /**
   * Check for duplicate bookings
   * @param context The booking creation context
   * @returns True if a duplicate booking exists, false otherwise
   */
  private async checkDuplicateBooking(
    context: BookingCreationContext,
  ): Promise<boolean> {
    return this.bookingRepository.checkDuplicateBooking(
      context.dto.bookingPageId,
      context.dto.customerEmail,
      context.dto.customerPhone,
    );
  }

  /**
   * Validate all booking slots in parallel for better performance
   * @param context The booking creation context
   */
  private async validateBookingSlots(
    context: BookingCreationContext,
  ): Promise<void> {
    console.log('Starting validation of booking slots');
    console.log(
      'Booking slots to validate:',
      JSON.stringify(context.dto.bookingSlots),
    );

    // For safety, validate each slot sequentially instead of in parallel
    // This ensures we don't miss any conflicts due to race conditions
    for (let index = 0; index < context.dto.bookingSlots.length; index++) {
      const slot = context.dto.bookingSlots[index];
      console.log(
        `Validating slot ${index + 1}/${context.dto.bookingSlots.length}: field=${slot.field}, date=${slot.date}, time=${slot.time}`,
      );

      const isSlotBooked = await this.bookingRepository.isSlotBooked(
        context.dto.bookingPageId,
        slot.field,
        slot.date,
        slot.time,
      );

      if (isSlotBooked) {
        console.log(`Slot ${index + 1} is already booked!`);
        context.validationResults.push({
          isValid: false,
          errorKey: 'booking/slot-already-booked' as keyof typeof ErrorMessages,
          errorMessage: `The slot on ${new Date(slot.date).toLocaleDateString()} at ${slot.time} for field ${slot.field} is already booked.`,
        });

        // We can return early if we find any booked slot
        return;
      } else {
        console.log(`Slot ${index + 1} is available`);
      }
    }

    console.log('All slots are available');
  }

  /**
   * Handle validation errors by throwing the first error encountered
   * @param context The booking creation context
   */
  private handleValidationErrors(context: BookingCreationContext): void {
    console.log(
      `Validation results: ${context.validationResults.length} results`,
    );

    // Log all validation errors for debugging
    context.validationResults.forEach((result, index) => {
      if (!result.isValid) {
        console.log(
          `Validation error ${index + 1}: ${result.errorKey} - ${result.errorMessage || ErrorMessages[result.errorKey]}`,
        );
      }
    });

    const firstError = context.validationResults.find(
      (result) => !result.isValid,
    );

    if (firstError) {
      console.log(`Throwing first validation error: ${firstError.errorKey}`);
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: firstError.errorKey,
        customMessage: firstError.errorMessage,
      });
    } else {
      console.log('No validation errors found');
    }
  }

  /**
   * Create the booking record in the database
   * @param context The booking creation context
   * @returns The created booking
   */
  private async createBookingRecord(
    context: BookingCreationContext,
  ): Promise<Booking> {
    console.log('Creating booking record in database');

    try {
      const bookingData = {
        ...context.dto,
        status: 'pending',
        createdAt: new Date(),
      };

      console.log(
        'Booking data to save:',
        JSON.stringify(bookingData, null, 2),
      );

      const booking = await this.bookingRepository.create(bookingData);

      if (!booking) {
        console.error(
          'Failed to create booking - repository returned null/undefined',
        );
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'booking/create-failed',
        });
      }

      console.log(`Booking created successfully with ID: ${booking._id}`);
      return booking;
    } catch (error) {
      console.error('Error creating booking record:', error);
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking/create-failed',
      });
    }
  }

  /**
   * Send notifications to the booking page owner and customer
   * @param context The booking creation context
   * @param booking The created booking
   */
  private async sendBookingNotifications(
    _context: BookingCreationContext,
    _booking: Booking,
  ): Promise<void> {
    // This is commented out for now, but the structure is in place for when it's needed
    // Send notification to the booking page owner
    // await this.notificationService.sendEmail({
    //   to: context.bookingPage.ownerId.toString(), // This should be the owner's email, but we're using ID for now
    //   subject: `New Booking: ${context.dto.customerName}`,
    //   text: `You have a new booking from ${context.dto.customerName} (${context.dto.customerEmail})`,
    // });
    // Send confirmation to the customer
    // await this.notificationService.sendEmail({
    //   to: context.dto.customerEmail,
    //   subject: 'Booking Confirmation',
    //   text: `Thank you for your booking. Your booking is pending confirmation.`,
    // });
  }

  /**
   * Handle errors that occur during booking creation
   * @param error The error that occurred
   */
  private handleBookingError(error: any): never {
    console.log('Booking error:', error);

    // If the error is already a structured error from ErrorHelper
    if (error.response) {
      console.log('Error response:', error.response);

      // Check if the error code is a valid key in our error messages
      const errorKey = error.response?.code as string;
      const isValidErrorKey = Object.keys(ErrorMessages).includes(errorKey);

      console.log(
        `Error key: ${errorKey}, isValidErrorKey: ${isValidErrorKey}`,
      );

      // If it's a valid error key, use it; otherwise, use a generic error
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: isValidErrorKey
          ? (errorKey as keyof typeof ErrorMessages)
          : 'booking/create-failed',
        customMessage:
          error.response?.message || ErrorMessages['booking/create-failed'],
      });
    }

    // If it's a MongoDB validation error
    if (error.name === 'ValidationError') {
      console.log('MongoDB validation error:', error);

      // Extract validation error messages
      const validationErrors = Object.values(error.errors || {})
        .map((err: any) => err.message)
        .join(', ');

      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking/create-failed',
        customMessage: `Validation failed: ${validationErrors}`,
      });
    }

    // For any other type of error
    console.error('Unhandled error type:', error);

    // This will always throw, so it satisfies the 'never' return type
    throw ErrorHelper.throwError({
      moduleErrors: ErrorMessages,
      key: 'booking/create-failed',
      customMessage:
        error.message ||
        'An unexpected error occurred while creating the booking',
    });
  }

  async findAll(options?: {
    bookingPageId?: string;
    customerEmail?: string;
  }): Promise<Booking[]> {
    const query: any = {};

    if (options?.bookingPageId) {
      query.bookingPageId = options.bookingPageId;
    }

    if (options?.customerEmail) {
      query.customerEmail = options.customerEmail;
    }

    return this.bookingRepository.find(query);
  }

  async findOne(id: string): Promise<Booking> {
    const booking = await this.bookingRepository.findById(id);
    if (!booking) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking/not-found',
      });
    }
    return booking;
  }

  async updateStatus(
    id: string,
    status: 'pending' | 'confirmed' | 'cancelled' | 'completed',
    ownerId: string,
  ): Promise<Booking> {
    // Check if booking exists
    const booking = await this.findOne(id);

    // Check if the user is the owner of the booking page
    const bookingPage = await this.bookingPageService.findOne(
      booking.bookingPageId.toString(),
      { ownerId },
    );

    if (!bookingPage) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking/access-denied',
      });
    }

    // Update the booking status
    const updatedBooking = await this.bookingRepository.update(id, {
      status,
      updatedAt: new Date(),
    });

    if (!updatedBooking) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking/update-failed',
      });
    }

    // Send notification to the customer
    // await this.notificationService.sendEmail({
    //   to: booking.customerEmail,
    //   subject: `Booking ${status.charAt(0).toUpperCase() + status.slice(1)}`,
    //   text: `Your booking has been ${status}.`,
    // });

    return updatedBooking;
  }

  /**
   * Get all booked slots for a specific booking page and date
   * @param bookingPageId The ID of the booking page
   * @param date The date to check (YYYY-MM-DD)
   * @returns Object containing booking page info and booked slots
   */
  async getBookedSlots(
    bookingPageId: string,
    date: string,
  ): Promise<{
    bookingPageId: string;
    date: string;
    bookedSlots: {
      field: string;
      time: string;
      date: string;
      bookingId: string;
      status: string;
    }[];
  }> {
    console.log(
      `Service: Getting booked slots for bookingPageId=${bookingPageId}, date=${date}`,
    );

    try {
      // Verify that the booking page exists
      const bookingPage = await this.bookingPageService.findOne(bookingPageId);

      if (!bookingPage) {
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'booking/booking-page-not-found',
        });
      }

      console.log(`Found booking page with ID: ${bookingPage._id}`);

      // Get all booked slots for this booking page and date
      const bookedSlots = await this.bookingRepository.getBookedSlots(
        bookingPageId,
        date,
      );

      return {
        bookingPageId,
        date,
        bookedSlots,
      };
    } catch (error) {
      console.error(`Error getting booked slots: ${error.message}`, error);

      // Re-throw the error if it's already a structured error
      if (error.response) {
        throw error;
      }

      // Otherwise, throw a generic error
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking/not-found',
        customMessage: 'Failed to get booked slots',
      });
    }
  }

  /**
   * Get booking slots with advanced filtering for agents
   * @param filters Filter options for booking slots
   * @param ownerId The ID of the agent (to verify ownership)
   * @returns Object containing filtered booking slots with pagination info
   */
  async getBookingSlotsWithFilter(
    filters: GetBookingSlotsFilterDto,
    ownerId: string,
  ): Promise<BookingSlotsFilterResponse> {
    console.log(
      `Service: Getting booking slots with filter for bookingPageId=${filters.bookingPageId}, ownerId=${ownerId}`,
    );

    try {
      // First, verify that the booking page exists and belongs to the agent
      const bookingPage = await this.bookingPageService.findOne(
        filters.bookingPageId,
        { ownerId },
      );

      if (!bookingPage) {
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'booking/access-denied',
        });
      }

      console.log(
        `Found booking page: ${bookingPage.name} (${bookingPage.slug})`,
      );

      // Get filtered booking slots from repository (using optimized aggregation)
      const result =
        await this.bookingRepository.getBookingSlotsWithFilterOptimized(
          filters,
        );

      return {
        ...result,
      };
    } catch (error) {
      console.error(
        `Error getting booking slots with filter: ${error.message}`,
        error,
      );

      // Re-throw the error if it's already a structured error
      if (error.response) {
        throw error;
      }

      // Otherwise, throw a generic error
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking/not-found',
        customMessage: 'Failed to get booking slots with filter',
      });
    }
  }

  /**
   * Get booking records with advanced filtering for agents
   * @param filters Filter options for booking records
   * @param ownerId The ID of the agent (to verify ownership)
   * @returns Object containing filtered booking records with pagination info
   */
  async getBookingsWithFilter(
    filters: BookingFilterOptions,
    ownerId: string,
  ): Promise<{
    data: Booking[];
    meta: {
      total: number;
      limit: number;
      offset: number;
    };
  }> {
    console.log(
      `Service: Getting booking records with filter for bookingPageId=${filters.bookingPageId}, ownerId=${ownerId}`,
    );

    try {
      // First, verify that the booking page exists and belongs to the agent
      const bookingPage = await this.bookingPageService.findOne(
        filters.bookingPageId,
        { ownerId },
      );

      if (!bookingPage) {
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'booking/access-denied',
        });
      }

      console.log(
        `Found booking page: ${bookingPage.name} (${bookingPage.slug})`,
      );

      // Get filtered booking records from repository
      const result =
        await this.bookingRepository.getBookingsWithFilter(filters);

      return {
        data: result.bookings,
        meta: {
          total: result.total,
          limit: result.limit,
          offset: result.offset,
        },
      };
    } catch (error) {
      console.error(
        `Error getting booking records with filter: ${error.message}`,
        error,
      );

      // Re-throw the error if it's already a structured error
      if (error.response) {
        throw error;
      }

      // Otherwise, throw a generic error
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking/not-found',
        customMessage: 'Failed to get booking records with filter',
      });
    }
  }

  /**
   * Get booking records with advanced filtering for agents
   * @param filters Filter options for booking records
   * @param ownerId The ID of the agent (to verify ownership)
   * @returns Object containing filtered booking records with pagination info
   */
  async getBookingsWithFilterBasic(
    filters: BookingFilterOptions,
    ownerId: string,
  ): Promise<{
    data: Booking[];
    meta: {
      total: number;
      limit: number;
      offset: number;
    };
  }> {
    console.log(
      `Service: Getting booking records with filter for bookingPageId=${filters.bookingPageId}, ownerId=${ownerId}`,
    );

    try {
      // First, verify that the booking page exists and belongs to the agent
      const bookingPage = await this.bookingPageService.findOne(
        filters.bookingPageId,
        { ownerId },
      );

      if (!bookingPage) {
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'booking/access-denied',
        });
      }

      console.log(
        `Found booking page: ${bookingPage.name} (${bookingPage.slug})`,
      );

      console.log('Filters:', filters);

      // Get filtered booking records from repository
      const result =
        await this.bookingRepository.getBookingsWithFilter(filters);

      return {
        data: result.bookings,
        meta: {
          total: result.total,
          limit: result.limit,
          offset: result.offset,
        },
      };
    } catch (error) {
      console.error(
        `Error getting booking records with filter: ${error.message}`,
        error,
      );

      // Re-throw the error if it's already a structured error
      if (error.response) {
        throw error;
      }

      // Otherwise, throw a generic error
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking/not-found',
        customMessage: 'Failed to get booking records with filter',
      });
    }
  }
}
