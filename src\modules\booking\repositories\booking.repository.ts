import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BaseRepository } from 'src/common/repositories/base.repository';
import { Booking } from '../schemas/booking.schema';
import { Model, Types } from 'mongoose';
import {
  BookingSlotWithDetails,
  BookingFilterOptions,
  BookingFilterResult,
  BOOKING_FILTER_DEFAULTS,
} from '../interfaces/booked-slot.interface';

@Injectable()
export class BookingRepository extends BaseRepository<Booking> {
  constructor(@InjectModel(Booking.name) private bookingModel: Model<Booking>) {
    super(bookingModel);
  }

  async findByBookingPageId(bookingPageId: string): Promise<Booking[]> {
    return this.bookingModel.find({ bookingPageId }).exec();
  }

  async findByCustomerEmail(email: string): Promise<Booking[]> {
    return this.bookingModel.find({ customerEmail: email }).exec();
  }

  /**
   * Override the create method from BaseRepository to add logging
   * @param createDto The booking data
   * @returns The created booking
   */
  async create(createDto: any): Promise<Booking> {
    console.log('Repository: Creating new booking');
    try {
      const createdDoc = new this.bookingModel(createDto);
      const savedDoc = await createdDoc.save();
      console.log(`Repository: Booking created with ID: ${savedDoc._id}`);
      return savedDoc;
    } catch (error) {
      console.error('Repository: Error creating booking:', error);
      throw error;
    }
  }

  /**
   * Check if a booking with the same customer information and booking page already exists
   * @param bookingPageId The ID of the booking page
   * @param customerEmail The email of the customer
   * @param customerPhone The phone number of the customer
   * @returns True if a duplicate booking exists, false otherwise
   */
  async checkDuplicateBooking(
    bookingPageId: string,
    customerEmail: string,
    customerPhone: string,
  ): Promise<boolean> {
    console.log(
      `Checking for duplicate booking: bookingPageId=${bookingPageId}, customerEmail=${customerEmail}, customerPhone=${customerPhone}`,
    );

    try {
      // Calculate the date 24 hours ago
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      console.log(
        `Checking for bookings created after: ${oneDayAgo.toISOString()}`,
      );

      // Use lean() for better performance since we only need to check existence
      const existingBooking = await this.bookingModel
        .findOne({
          bookingPageId,
          customerEmail,
          customerPhone,
          status: { $in: ['pending', 'confirmed'] }, // Only check active bookings
          createdAt: { $gte: oneDayAgo }, // Only check bookings created in the last 24 hours
        })
        .lean()
        .exec();

      if (existingBooking) {
        console.log(`Found duplicate booking: ${existingBooking._id}`);
        return true;
      }

      console.log('No duplicate booking found');
      return false;
    } catch (error) {
      console.error('Error checking for duplicate booking:', error);
      // In case of error, assume there's a duplicate to prevent creating potentially duplicate bookings
      return true;
    }
  }

  /**
   * Check if a booking slot is already booked
   * @param bookingPageId The ID of the booking page
   * @param field The field ID
   * @param date The date of the booking
   * @param time The time of the booking
   * @returns True if the slot is already booked, false otherwise
   */
  async isSlotBooked(
    bookingPageId: string,
    field: string,
    date: string,
    time: string,
  ): Promise<boolean> {
    console.log(
      `Checking if slot is booked: bookingPageId=${bookingPageId}, field=${field}, date=${date}, time=${time}`,
    );

    try {
      // Convert date string to Date object for comparison
      const bookingDate = new Date(date);

      // Get start and end of the day for date range query
      const startOfDay = new Date(bookingDate);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(bookingDate);
      endOfDay.setHours(23, 59, 59, 999);

      console.log(
        `Date range: ${startOfDay.toISOString()} to ${endOfDay.toISOString()}`,
      );

      // First, find all bookings for this booking page with the specified field and time
      const bookings = await this.bookingModel
        .find({
          bookingPageId,
          status: { $in: ['pending', 'confirmed'] },
          'bookingSlots.field': field,
          'bookingSlots.time': time,
        })
        .exec();

      console.log(
        `Found ${bookings.length} bookings with matching field and time`,
      );

      // Then check if any of these bookings have a slot on the same date
      for (const booking of bookings) {
        for (const slot of booking.bookingSlots) {
          if (slot.field === field && slot.time === time) {
            const slotDate = new Date(slot.date);

            // Compare year, month, and day
            if (
              slotDate.getFullYear() === bookingDate.getFullYear() &&
              slotDate.getMonth() === bookingDate.getMonth() &&
              slotDate.getDate() === bookingDate.getDate()
            ) {
              console.log(
                `Found conflicting booking: ${booking._id}, slot date: ${slotDate.toISOString()}`,
              );
              return true;
            }
          }
        }
      }

      console.log('No conflicting bookings found');
      return false;
    } catch (error) {
      console.error('Error checking if slot is booked:', error);
      // In case of error, assume the slot is booked to prevent double booking
      return true;
    }
  }

  /**
   * Get all booked slots for a specific booking page and date
   * @param bookingPageId The ID of the booking page
   * @param date The date to check (YYYY-MM-DD)
   * @returns Array of booked slots with field, time, booking ID, and status
   */
  async getBookedSlots(
    bookingPageId: string,
    date: string,
  ): Promise<
    {
      field: string;
      time: string;
      date: string;
      bookingId: string;
      status: string;
    }[]
  > {
    console.log(
      `Getting booked slots for bookingPageId=${bookingPageId}, date=${date}`,
    );

    try {
      // Parse the date string to a Date object
      const bookingDate = new Date(date);

      // Get start and end of the day for date range query
      const startOfDay = new Date(bookingDate);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(bookingDate);
      endOfDay.setHours(23, 59, 59, 999);

      console.log(
        `Date range: ${startOfDay.toISOString()} to ${endOfDay.toISOString()}`,
      );

      // Find all bookings for this booking page (including all statuses)
      const bookings = await this.bookingModel
        .find({
          bookingPageId,
          // Include all statuses to show complete booking history
          // status: { $in: ['pending', 'confirmed', 'cancelled', 'completed'] },
        })
        .exec();

      console.log(`Found ${bookings.length} bookings for this booking page`);

      // Extract all booked slots for the specified date
      const bookedSlots: {
        field: string;
        time: string;
        date: string;
        bookingId: string;
        status: string;
      }[] = [];

      for (const booking of bookings) {
        for (const slot of booking.bookingSlots) {
          const slotDate = new Date(slot.date);

          // Compare year, month, and day to check if the slot is on the specified date
          if (
            slotDate.getFullYear() === bookingDate.getFullYear() &&
            slotDate.getMonth() === bookingDate.getMonth() &&
            slotDate.getDate() === bookingDate.getDate()
          ) {
            bookedSlots.push({
              field: slot.field,
              time: slot.time,
              date: slotDate.toISOString(),
              bookingId: booking._id.toString(),
              status: booking.status, // Add the booking status
            });
          }
        }
      }

      console.log(
        `Found ${bookedSlots.length} booked slots for the specified date`,
      );

      // Log the statuses for debugging
      const statusCounts = bookedSlots.reduce(
        (acc, slot) => {
          acc[slot.status] = (acc[slot.status] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      );

      console.log('Status counts:', statusCounts);

      return bookedSlots;
    } catch (error) {
      console.error(`Error getting booked slots: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Get booking slots with advanced filtering options
   * @param filters Filter options for booking slots
   * @returns Object containing filtered booking slots with pagination info
   */
  async getBookingSlotsWithFilter(filters: {
    bookingPageId: string;
    date?: string;
    dateFrom?: string;
    dateTo?: string;
    field?: string;
    time?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    slots: {
      field: string;
      fieldName: string;
      time: string;
      date: string;
      bookingId: string;
      status: string;
      customerName: string;
      customerEmail: string;
      customerPhone: string;
      quantity: number;
      paymentMethod: string;
      createdAt: Date;
    }[];
    total: number;
    limit: number;
    offset: number;
  }> {
    console.log('Getting booking slots with filters:', filters);

    try {
      // Build the base query
      const query: any = {
        bookingPageId: filters.bookingPageId,
      };

      // Add status filter if provided
      if (filters.status) {
        query.status = filters.status;
      }

      console.log('Base query:', query);

      // Get all bookings that match the base criteria
      const bookings = await this.bookingModel.find(query).exec();

      console.log(`Found ${bookings.length} bookings for filtering`);

      // Extract and filter slots
      let allSlots: {
        field: string;
        fieldName: string;
        time: string;
        date: string;
        bookingId: string;
        status: string;
        customerName: string;
        customerEmail: string;
        customerPhone: string;
        quantity: number;
        paymentMethod: string;
        createdAt: Date;
      }[] = [];

      for (const booking of bookings) {
        for (const slot of booking.bookingSlots) {
          const slotDate = new Date(slot.date);

          // Apply date filters
          let includeSlot = true;

          // Specific date filter
          if (filters.date) {
            const filterDate = new Date(filters.date);
            if (
              slotDate.getFullYear() !== filterDate.getFullYear() ||
              slotDate.getMonth() !== filterDate.getMonth() ||
              slotDate.getDate() !== filterDate.getDate()
            ) {
              includeSlot = false;
            }
          }

          // Date range filter
          if (filters.dateFrom && includeSlot) {
            const fromDate = new Date(filters.dateFrom);
            fromDate.setHours(0, 0, 0, 0);
            if (slotDate < fromDate) {
              includeSlot = false;
            }
          }

          if (filters.dateTo && includeSlot) {
            const toDate = new Date(filters.dateTo);
            toDate.setHours(23, 59, 59, 999);
            if (slotDate > toDate) {
              includeSlot = false;
            }
          }

          // Field filter
          if (filters.field && includeSlot) {
            if (slot.field !== filters.field) {
              includeSlot = false;
            }
          }

          // Time filter
          if (filters.time && includeSlot) {
            if (slot.time !== filters.time) {
              includeSlot = false;
            }
          }

          if (includeSlot) {
            allSlots.push({
              field: slot.field,
              fieldName: slot.fieldName || slot.field,
              time: slot.time,
              date: slotDate.toISOString(),
              bookingId: booking._id.toString(),
              status: booking.status,
              customerName: booking.customerName,
              customerEmail: booking.customerEmail,
              customerPhone: booking.customerPhone,
              quantity: booking.quantity || 1,
              paymentMethod: booking.paymentMethod,
              createdAt: booking.createdAt,
            });
          }
        }
      }

      // Sort by date and time
      allSlots.sort((a, b) => {
        const dateCompare =
          new Date(a.date).getTime() - new Date(b.date).getTime();
        if (dateCompare !== 0) return dateCompare;
        return a.time.localeCompare(b.time);
      });

      const total = allSlots.length;
      const limit = filters.limit || 20;
      const offset = filters.offset || 0;

      // Apply pagination
      const paginatedSlots = allSlots.slice(offset, offset + limit);

      console.log(
        `Returning ${paginatedSlots.length} slots out of ${total} total (limit: ${limit}, offset: ${offset})`,
      );

      return {
        slots: paginatedSlots,
        total,
        limit,
        offset,
      };
    } catch (error) {
      console.error(
        `Error getting booking slots with filter: ${error.message}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get booking slots with advanced filtering using MongoDB aggregation (more efficient)
   * @param filters Filter options for booking slots
   * @returns Object containing filtered booking slots with pagination info
   */
  async getBookingSlotsWithFilterOptimized(filters: {
    bookingPageId: string;
    date?: string;
    dateFrom?: string;
    dateTo?: string;
    field?: string;
    time?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    slots: BookingSlotWithDetails[];
    total: number;
    limit: number;
    offset: number;
  }> {
    console.log('Getting booking slots with filters (optimized):', filters);

    try {
      const pipeline: any[] = [];

      // Match stage - filter bookings
      const matchStage: any = {
        bookingPageId: filters.bookingPageId,
      };

      if (filters.status) {
        matchStage.status = filters.status;
      }

      pipeline.push({ $match: matchStage });

      // Unwind bookingSlots to work with individual slots
      pipeline.push({ $unwind: '$bookingSlots' });

      // Add computed fields for easier filtering
      pipeline.push({
        $addFields: {
          'bookingSlots.slotDate': {
            $dateFromString: { dateString: '$bookingSlots.date' },
          },
        },
      });

      // Additional match stage for slot-specific filters
      const slotMatchStage: any = {};

      // Date filters
      if (filters.date) {
        const filterDate = new Date(filters.date);
        const startOfDay = new Date(filterDate);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(filterDate);
        endOfDay.setHours(23, 59, 59, 999);

        slotMatchStage['bookingSlots.slotDate'] = {
          $gte: startOfDay,
          $lte: endOfDay,
        };
      } else {
        // Date range filters
        if (filters.dateFrom || filters.dateTo) {
          slotMatchStage['bookingSlots.slotDate'] = {};

          if (filters.dateFrom) {
            const fromDate = new Date(filters.dateFrom);
            fromDate.setHours(0, 0, 0, 0);
            slotMatchStage['bookingSlots.slotDate'].$gte = fromDate;
          }

          if (filters.dateTo) {
            const toDate = new Date(filters.dateTo);
            toDate.setHours(23, 59, 59, 999);
            slotMatchStage['bookingSlots.slotDate'].$lte = toDate;
          }
        }
      }

      // Field and time filters
      if (filters.field) {
        slotMatchStage['bookingSlots.field'] = filters.field;
      }

      if (filters.time) {
        slotMatchStage['bookingSlots.time'] = filters.time;
      }

      if (Object.keys(slotMatchStage).length > 0) {
        pipeline.push({ $match: slotMatchStage });
      }

      // Project the final structure
      pipeline.push({
        $project: {
          field: '$bookingSlots.field',
          fieldName: {
            $ifNull: ['$bookingSlots.fieldName', '$bookingSlots.field'],
          },
          time: '$bookingSlots.time',
          date: '$bookingSlots.date',
          bookingId: { $toString: '$_id' },
          status: '$status',
          customerName: '$customerName',
          customerEmail: '$customerEmail',
          customerPhone: '$customerPhone',
          quantity: { $ifNull: ['$quantity', 1] },
          paymentMethod: '$paymentMethod',
          createdAt: '$createdAt',
          sortDate: '$bookingSlots.slotDate',
        },
      });

      // Sort by date and time
      pipeline.push({
        $sort: {
          sortDate: 1,
          time: 1,
        },
      });

      // Count total for pagination
      const countPipeline = [...pipeline, { $count: 'total' }];
      const countResult = await this.bookingModel
        .aggregate(countPipeline)
        .exec();
      const total = countResult.length > 0 ? countResult[0].total : 0;

      // Apply pagination
      const limit = +(filters.limit || 20);
      const offset = +(filters.offset || 0);

      pipeline.push({ $skip: offset });
      pipeline.push({ $limit: limit });

      // Remove sortDate from final result
      pipeline.push({
        $project: {
          sortDate: 0,
        },
      });

      // Execute the aggregation
      const slots = await this.bookingModel.aggregate(pipeline).exec();

      console.log(
        `Returning ${slots.length} slots out of ${total} total (limit: ${limit}, offset: ${offset})`,
      );

      return {
        slots: slots as BookingSlotWithDetails[],
        total,
        limit,
        offset,
      };
    } catch (error) {
      console.error(
        `Error getting booking slots with filter (optimized): ${error.message}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get booking records with advanced filtering (returns full booking documents)
   * @param filters Filter options for booking records
   * @returns Object containing filtered booking records with pagination info
   */
  async getBookingsWithFilter(
    filters: BookingFilterOptions,
  ): Promise<BookingFilterResult> {
    console.log('Getting booking records with filters:', filters);

    try {
      // Build aggregation pipeline
      const pipeline = this.buildBookingFilterPipeline(filters);
      console.log('Filters:', filters);
      console.log('pipeline:', pipeline);

      // Get total count for pagination (more efficient approach)
      const [countResult, bookings] = await Promise.all([
        this.getBookingCount(filters),
        this.executeBookingQuery(pipeline, filters.limit, filters.offset),
      ]);

      const limit = this.parseNumber(
        filters.limit,
        BOOKING_FILTER_DEFAULTS.LIMIT,
      );
      const offset = this.parseNumber(
        filters.offset,
        BOOKING_FILTER_DEFAULTS.OFFSET,
      );

      console.log(
        `Returning ${bookings.length} bookings out of ${countResult} total (limit: ${limit}, offset: ${offset})`,
      );

      return {
        bookings: bookings as Booking[],
        total: countResult,
        limit,
        offset,
      };
    } catch (error) {
      console.error(
        `Error getting booking records with filter: ${error.message}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Build the main aggregation pipeline for booking filters
   */
  private buildBookingFilterPipeline(filters: {
    bookingPageId: string;
    date?: string;
    dateFrom?: string;
    dateTo?: string;
    field?: string;
    time?: string;
    status?: string;
  }): any[] {
    const pipeline: any[] = [];

    // Stage 1: Match basic booking criteria
    pipeline.push({ $match: this.buildBasicMatchStage(filters) });

    // Stage 2: Filter by slot criteria if needed
    const slotFilter = this.buildSlotFilterStage(filters);
    if (slotFilter) {
      pipeline.push(slotFilter);
    }

    // Stage 3: Sort by creation date (newest first)
    pipeline.push({ $sort: { createdAt: -1 } });

    return pipeline;
  }

  /**
   * Build basic match stage for booking-level filters
   */
  private buildBasicMatchStage(filters: {
    bookingPageId: string;
    status?: string;
  }): any {
    const matchStage: any = {
      bookingPageId: new Types.ObjectId(filters.bookingPageId),
    };

    if (filters.status) {
      matchStage.status = filters.status;
    }

    return matchStage;
  }

  /**
   * Build slot filter stage for slot-level filters
   */
  private buildSlotFilterStage(filters: {
    date?: string;
    dateFrom?: string;
    dateTo?: string;
    field?: string;
    time?: string;
  }): any | null {
    const hasSlotFilters =
      filters.date ||
      filters.dateFrom ||
      filters.dateTo ||
      filters.field ||
      filters.time;

    if (!hasSlotFilters) {
      return null;
    }

    const slotConditions = this.buildSlotConditions(filters);

    return {
      $match: {
        $expr: {
          $gt: [
            {
              $size: {
                $filter: {
                  input: '$bookingSlots',
                  as: 'slot',
                  cond:
                    slotConditions.length > 1
                      ? { $and: slotConditions }
                      : slotConditions[0] || true,
                },
              },
            },
            0,
          ],
        },
      },
    };
  }

  /**
   * Build slot-level filter conditions
   */
  private buildSlotConditions(filters: {
    date?: string;
    dateFrom?: string;
    dateTo?: string;
    field?: string;
    time?: string;
  }): any[] {
    const conditions: any[] = [];

    // Date filters
    const dateCondition = this.buildDateCondition(filters);
    if (dateCondition) {
      conditions.push(dateCondition);
    }

    // Field filter
    if (filters.field) {
      conditions.push({ $eq: ['$$slot.field', filters.field] });
    }

    // Time filter
    if (filters.time) {
      conditions.push({ $eq: ['$$slot.time', filters.time] });
    }

    return conditions;
  }

  /**
   * Build date filter condition
   */
  private buildDateCondition(filters: {
    date?: string;
    dateFrom?: string;
    dateTo?: string;
  }): any | null {
    // Specific date has priority over date range
    if (filters.date) {
      const { startOfDay, endOfDay } = this.getDateRange(filters.date);
      return {
        $and: [
          {
            $gte: [
              { $dateFromString: { dateString: '$$slot.date' } },
              startOfDay,
            ],
          },
          {
            $lte: [
              { $dateFromString: { dateString: '$$slot.date' } },
              endOfDay,
            ],
          },
        ],
      };
    }

    // Date range filters
    const rangeConditions: any[] = [];

    if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom);
      fromDate.setHours(0, 0, 0, 0);
      rangeConditions.push({
        $gte: [{ $dateFromString: { dateString: '$$slot.date' } }, fromDate],
      });
    }

    if (filters.dateTo) {
      const toDate = new Date(filters.dateTo);
      toDate.setHours(23, 59, 59, 999);
      rangeConditions.push({
        $lte: [{ $dateFromString: { dateString: '$$slot.date' } }, toDate],
      });
    }

    return rangeConditions.length > 0
      ? rangeConditions.length > 1
        ? { $and: rangeConditions }
        : rangeConditions[0]
      : null;
  }

  /**
   * Get start and end of day for a date string
   */
  private getDateRange(dateString: string): {
    startOfDay: Date;
    endOfDay: Date;
  } {
    const date = new Date(dateString);
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    return { startOfDay, endOfDay };
  }

  /**
   * Get total count of bookings matching filters (optimized for count only)
   */
  private async getBookingCount(filters: {
    bookingPageId: string;
    date?: string;
    dateFrom?: string;
    dateTo?: string;
    field?: string;
    time?: string;
    status?: string;
  }): Promise<number> {
    const pipeline = this.buildBookingFilterPipeline(filters);
    pipeline.push({ $count: 'total' });

    const countResult = await this.bookingModel.aggregate(pipeline).exec();
    return countResult.length > 0 ? countResult[0].total : 0;
  }

  /**
   * Execute the booking query with pagination
   */
  private async executeBookingQuery(
    pipeline: any[],
    limit?: string | number,
    offset?: string | number,
  ): Promise<Booking[]> {
    const queryPipeline = [...pipeline];

    // Parse and apply pagination
    const parsedOffset = this.parseNumber(
      offset,
      BOOKING_FILTER_DEFAULTS.OFFSET,
    );
    const parsedLimit = this.parseNumber(limit, BOOKING_FILTER_DEFAULTS.LIMIT);

    if (parsedOffset > 0) {
      queryPipeline.push({ $skip: parsedOffset });
    }

    queryPipeline.push({ $limit: parsedLimit });

    return await this.bookingModel.aggregate(queryPipeline).exec();
  }

  /**
   * Parse number from string or number, with fallback to default value
   */
  private parseNumber(
    value: string | number | undefined,
    defaultValue: number,
  ): number {
    if (value === undefined || value === null) {
      return defaultValue;
    }

    const parsed = typeof value === 'string' ? parseInt(value, 10) : value;

    // Check if parsed value is valid number and positive
    if (isNaN(parsed) || parsed < 0) {
      return defaultValue;
    }

    return parsed;
  }
}
