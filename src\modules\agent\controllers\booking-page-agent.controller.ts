import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Query,
  ValidationPipe,
} from '@nestjs/common';
import { BookingPageService } from '../../booking-page/booking-page.service';
import { BookingService } from '../../booking/booking.service';
import { CreateBookingPageDto } from '../../booking-page/dto/create-booking-page.dto';
import { UpdateBookingPageDto } from '../../booking-page/dto/update-booking-page.dto';
import { GetBookingSlotsFilterDto } from '../../booking/dto/get-booking-slots-filter.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { AuthGuard } from 'src/common/guards/auth.guard';
import { GetUser } from 'src/common/decorators/get-user.decorator';

@ApiTags('agent/booking-page')
@Controller('agent/booking-page')
@UseGuards(AuthGuard) // Tất cả các route đều yêu cầu xác thực
@ApiBearerAuth()
export class BookingPageAgentController {
  constructor(
    private readonly bookingPageService: BookingPageService,
    private readonly bookingService: BookingService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new booking page' })
  @ApiResponse({
    status: 201,
    description: 'Booking page created successfully',
  })
  async create(
    @Body(new ValidationPipe()) createBookingPageDto: CreateBookingPageDto,
    @GetUser('_id') userId: string,
  ) {
    // Set the owner ID to the current user's ID
    createBookingPageDto.ownerId = userId;
    const data = await this.bookingPageService.create(createBookingPageDto);
    return { data };
  }

  @Get()
  @ApiOperation({ summary: 'Get all booking pages owned by the agent' })
  @ApiResponse({ status: 200, description: 'Return all booking pages' })
  @ApiQuery({
    name: 'includePrivate',
    required: false,
    type: Boolean,
    description: 'Include private pages',
  })
  async findAll(
    @GetUser('_id') userId: string,
    @Query('includePrivate') includePrivate: boolean = true,
  ) {
    // Agent can see all their own pages, including private ones
    const data = await this.bookingPageService.findAll({
      ownerId: userId,
      includePrivate: includePrivate, // Agents always see their own private pages
    });
    return { data };
  }

  @Get('slug/:slug')
  @ApiOperation({ summary: 'Get a booking page by slug' })
  @ApiResponse({ status: 200, description: 'Return the booking page' })
  @ApiResponse({ status: 404, description: 'Booking page not found' })
  async findBySlug(
    @Param('slug') slug: string,
    @GetUser('_id') userId: string,
  ) {
    // Agent can see their own pages
    const data = await this.bookingPageService.findBySlug(slug, {
      ownerId: userId,
    });
    return { data };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a booking page by ID' })
  @ApiResponse({ status: 200, description: 'Return the booking page' })
  @ApiResponse({ status: 404, description: 'Booking page not found' })
  async findOne(@Param('id') id: string, @GetUser('_id') userId: string) {
    // Agent can see their own pages
    const data = await this.bookingPageService.findOne(id, { ownerId: userId });
    return { data };
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a booking page' })
  @ApiResponse({
    status: 200,
    description: 'Booking page updated successfully',
  })
  @ApiResponse({ status: 404, description: 'Booking page not found' })
  async update(
    @Param('id') id: string,
    @Body(new ValidationPipe()) updateBookingPageDto: UpdateBookingPageDto,
    @GetUser('_id') userId: string,
  ) {
    const data = await this.bookingPageService.update(
      id,
      updateBookingPageDto,
      userId,
    );
    return { data };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a booking page' })
  @ApiResponse({
    status: 200,
    description: 'Booking page deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Booking page not found' })
  async remove(@Param('id') id: string, @GetUser('_id') userId: string) {
    await this.bookingPageService.remove(id, userId);
    return { message: 'Booking page deleted successfully' };
  }

  @Get(':id/bookings')
  @ApiOperation({
    summary: 'Get all bookings for a booking page with advanced filtering',
    description:
      'Get all booking records for a specific booking page with optional filters for date, field, time, status, etc. Supports pagination.',
  })
  @ApiResponse({
    status: 200,
    description: 'Return filtered booking records with pagination info',
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Success' },
          },
        },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              _id: { type: 'string', example: '68261cde094b5f3e38d3ae25' },
              slug: { type: 'string', example: 'booking-123' },
              customerName: { type: 'string', example: 'John Doe' },
              customerEmail: {
                type: 'string',
                example: '<EMAIL>',
              },
              customerPhone: { type: 'string', example: '+84123456789' },
              bookingPageId: {
                type: 'string',
                example: '68261cde094b5f3e38d3ae25',
              },
              bookingDate: { type: 'string', example: '2025-05-22' },
              bookingSlots: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    date: {
                      type: 'string',
                      example: '2025-05-22T14:48:29.780Z',
                    },
                    field: { type: 'string', example: 'field-1' },
                    fieldName: {
                      type: 'string',
                      example: 'Football Field 1',
                    },
                    time: { type: 'string', example: '08:00' },
                  },
                },
              },
              paymentMethod: { type: 'string', example: 'COD' },
              quantity: { type: 'number', example: 1 },
              status: { type: 'string', example: 'confirmed' },
              createdAt: {
                type: 'string',
                example: '2025-05-22T14:48:29.780Z',
              },
              updatedAt: {
                type: 'string',
                example: '2025-05-22T14:48:29.780Z',
              },
            },
          },
        },
        meta: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 100 },
            limit: { type: 'number', example: 50 },
            offset: { type: 'number', example: 0 },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Booking page not found' })
  @ApiResponse({ status: 403, description: 'Access denied - not the owner' })
  @ApiQuery({
    name: 'date',
    required: false,
    type: String,
    description: 'Specific date to filter (YYYY-MM-DD)',
    example: '2025-05-22',
  })
  @ApiQuery({
    name: 'dateFrom',
    required: false,
    type: String,
    description: 'Start date for date range filter (YYYY-MM-DD)',
    example: '2025-05-20',
  })
  @ApiQuery({
    name: 'dateTo',
    required: false,
    type: String,
    description: 'End date for date range filter (YYYY-MM-DD)',
    example: '2025-05-25',
  })
  @ApiQuery({
    name: 'field',
    required: false,
    type: String,
    description: 'Field ID to filter',
    example: 'field-1',
  })
  @ApiQuery({
    name: 'time',
    required: false,
    type: String,
    description: 'Time slot to filter',
    example: '08:00',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ['pending', 'confirmed', 'cancelled', 'completed'],
    description: 'Booking status to filter',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of results to return (1-1000)',
    example: 50,
  })
  @ApiQuery({
    name: 'offset',
    required: false,
    type: Number,
    description: 'Number of results to skip',
    example: 0,
  })
  async getBookings(
    @Param('id') bookingPageId: string,
    @GetUser('_id') userId: string,
    @Query() query: GetBookingSlotsFilterDto,
  ) {
    // Merge the bookingPageId from params with query filters
    const filters = {
      ...query,
      bookingPageId,
    };

    return await this.bookingService.getBookingsWithFilter(filters, userId);
  }
}
